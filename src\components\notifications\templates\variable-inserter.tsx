/**
 * Componente para inserção rápida de variáveis no template
 * Mostra variáveis categorizadas com preview dos valores
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAcademyData } from '@/hooks/notifications/use-template-preview';
import type { TemplateVariable } from '@/services/notifications/types/notification-types';

interface VariableInserterProps {
  variables: TemplateVariable[];
  exampleVariables: Record<string, any>;
  onInsertVariable: (variableKey: string) => void;
  className?: string;
}

export function VariableInserter({ 
  variables, 
  exampleVariables, 
  onInsertVariable, 
  className 
}: VariableInserterProps) {
  const { academyData } = useAcademyData();

  // Combinar variáveis de exemplo com dados reais da academia
  const enrichedVariables = React.useMemo((): Record<string, any> => {
    const academyVariables = {
      academyName: academyData?.name || 'Academia Exemplo',
      academyLogo: academyData?.logo_url || '',
      primaryColor: academyData?.primary_color || '#333333',
      secondaryColor: academyData?.secondary_color || '#666666',
      academySlug: academyData?.slug || 'academia-exemplo'
    };

    return {
      ...exampleVariables,
      ...academyVariables
    };
  }, [exampleVariables, academyData]);

  // Agrupar variáveis por categoria
  const categorizedVariables = React.useMemo(() => {
    const categories: Record<string, TemplateVariable[]> = {};

    variables.forEach((variable) => {
      const category = variable.category || 'Outras';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(variable);
    });

    // Ordenar categorias por importância
    const orderedCategories: Record<string, TemplateVariable[]> = {};
    const categoryOrder = ['academy', 'student', 'payment', 'class', 'enrollment', 'event', 'system'];
    const categoryNames = {
      academy: 'Academia',
      student: 'Estudante',
      payment: 'Pagamento',
      class: 'Aula',
      enrollment: 'Matrícula',
      event: 'Evento',
      system: 'Sistema'
    };

    categoryOrder.forEach(category => {
      const categoryKey = Object.keys(categories).find(key =>
        key.toLowerCase() === category
      );
      if (categoryKey && categories[categoryKey]) {
        const displayName = categoryNames[category as keyof typeof categoryNames] || categoryKey;
        orderedCategories[displayName] = categories[categoryKey];
      }
    });

    // Adicionar categorias restantes
    Object.keys(categories).forEach(category => {
      const displayName = categoryNames[category.toLowerCase() as keyof typeof categoryNames] || category;
      if (!orderedCategories[displayName]) {
        orderedCategories[displayName] = categories[category];
      }
    });

    return orderedCategories;
  }, [variables]);

  const getVariablePreview = (variableKey: string): string => {
    const value = enrichedVariables[variableKey];
    if (value === undefined || value === null) {
      return 'Não definido';
    }

    if (typeof value === 'string' && value.length > 30) {
      return value.substring(0, 30) + '...';
    }

    return String(value);
  };

  const isAcademyVariable = (variableKey: string): boolean => {
    return ['academyName', 'academyLogo', 'primaryColor', 'secondaryColor', 'academySlug'].includes(variableKey);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Variáveis Disponíveis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(categorizedVariables).map(([categoryName, categoryVariables]) => (
            <div key={categoryName}>
              <h4 className="font-medium text-sm text-muted-foreground mb-3 uppercase tracking-wide">
                {categoryName}
              </h4>
              <div className="space-y-2">
                {categoryVariables.map((variable) => {
                  const preview = getVariablePreview(variable.variable_key);
                  const isAcademy = isAcademyVariable(variable.variable_key);
                  
                  return (
                    <div
                      key={variable.id}
                      className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors group"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-1 font-mono text-xs"
                            onClick={() => onInsertVariable(variable.variable_key)}
                          >
                            {`{{${variable.variable_key}}}`}
                          </Button>
                          {variable.is_required && (
                            <Badge variant="destructive" className="text-xs">
                              Obrigatória
                            </Badge>
                          )}
                          {isAcademy && (
                            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                              Dados Reais
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mb-1">
                          {variable.description}
                        </p>
                        <div className="text-xs text-muted-foreground/80">
                          <span className="font-medium">Preview:</span>{' '}
                          <span className="font-mono bg-muted px-1 py-0.5 rounded">
                            {preview}
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onInsertVariable(variable.variable_key)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        Inserir
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Dica de uso */}
        <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
          <div className="text-xs text-blue-700 dark:text-blue-300">
            <div className="font-medium mb-1">💡 Dica:</div>
            <div>
              Clique em uma variável para inseri-la no template. As variáveis marcadas como "Dados Reais" 
              mostram informações atuais da sua academia no preview.
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
