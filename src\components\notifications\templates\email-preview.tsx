/**
 * Componente de preview de email em tempo real
 * Simula a aparência real de um email com dados da academia
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Skeleton } from '@/components/ui/skeleton';
import { useTemplateRenderWithAcademy, useAcademyData } from '@/hooks/notifications/use-template-preview';

interface EmailPreviewProps {
  subject: string;
  body: string;
  variables: Record<string, any>;
  className?: string;
}

export function EmailPreview({ subject, body, variables, className }: EmailPreviewProps) {
  const { rendered: renderedSubject } = useTemplateRenderWithAcademy(subject, variables, 200);
  const { rendered: renderedBody } = useTemplateRenderWithAcademy(body, variables, 200);
  const { academyData, loading } = useAcademyData();

  if (loading) {
    return <EmailPreviewSkeleton />;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Preview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border">
          {/* Container do email */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden max-w-2xl mx-auto">
            {/* Barra superior sutil simulando cliente de email */}
            <div className="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b flex items-center gap-2">
              <div className="flex gap-1">
                <div className="w-3 h-3 rounded-full bg-red-400"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                {renderedSubject || 'Sem assunto'}
              </div>
            </div>

            {/* Conteúdo do email */}
            <div className="p-0">
              <div
                className="prose prose-sm max-w-none dark:prose-invert"
                style={{
                  '--tw-prose-links': academyData?.primary_color || '#3b82f6',
                  '--tw-prose-headings': academyData?.secondary_color || '#1f2937'
                } as React.CSSProperties}
                dangerouslySetInnerHTML={{
                  __html: renderedBody || '<div class="p-6 text-gray-500 italic text-center">Digite o conteúdo do template...</div>'
                }}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function EmailPreviewSkeleton() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-5 w-20" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden max-w-2xl mx-auto">
            {/* Barra superior skeleton */}
            <div className="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b flex items-center gap-2">
              <div className="flex gap-1">
                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
              </div>
              <Skeleton className="h-3 w-32 ml-2" />
            </div>

            {/* Conteúdo skeleton */}
            <div className="p-6 space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
